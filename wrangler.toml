name = "site-bot"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 定时任务配置 - 每8小时执行一次（0点、8点、16点）
[triggers]
crons = ["0 0,8,16 * * *"]

# KV 存储配置
[[kv_namespaces]]
binding = "SITEMAP_STORAGE"
id = "3740c0cb5de94825904df0cbbf0a8888"
preview_id = "e6508554ee9245d0bb56ba68d7af2539"

# 环境变量
[vars]
NODE_ENV = "production"

# 生产环境配置
[env.production]
name = "site-bot-prod"

# 开发环境配置
[env.staging]
name = "site-bot-staging" 